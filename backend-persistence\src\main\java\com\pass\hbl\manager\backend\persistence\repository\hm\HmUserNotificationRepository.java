package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.entity.hm.HmUserNotification;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public interface HmUserNotificationRepository extends PagingAndSortingRepository<HmUserNotification, UUID> {

    List<HmUserNotification> findByUserIdInAndCreatedAtAfterOrderByCreatedAtDesc(List<UUID> userIds, LocalDateTime earliestDate);

    void deleteByUserId(UUID userId);

    // Batch deletion methods for data cleanup
    @Modifying
    @Query(value = "DELETE FROM hm.user_notification WHERE id IN (SELECT id FROM hm.user_notification LIMIT :chunkSize)", nativeQuery = true)
    int deleteUserNotificationsBatch(@Param("chunkSize") int chunkSize);

    @Query(value = "SELECT COUNT(*) FROM hm.user_notification", nativeQuery = true)
    long countAllUserNotifications();
}
