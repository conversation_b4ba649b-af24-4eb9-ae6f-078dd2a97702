package com.pass.hbl.manager.backend.restservice.controller.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupRequest;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupResponse;
import com.pass.hbl.manager.backend.persistence.service.admin.OldSeasonDataCleanupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Admin controller for data cleanup operations.
 * Provides endpoints for cleaning up old season data from the database.
 */
@RestController
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
@Validated
@Slf4j
@Tag(name = "admin-data-cleanup", description = "Admin API for data cleanup operations")
public class DataCleanupController {

    @Autowired
    private OldSeasonDataCleanupService cleanupService;

    /**
     * Endpoint to delete old season data from specified tables.
     * Supports both dry-run mode for preview and actual deletion.
     * 
     * @param request The cleanup request parameters
     * @return DataCleanupResponse with operation results
     */
    @Operation(
        summary = "Delete old season data",
        description = "Deletes data from specified tables. Supports dry-run mode for preview. " +
                     "Tables are processed in dependency order to avoid foreign key constraint violations."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Data cleanup completed successfully",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = DataCleanupResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request parameters",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = DataCleanupResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Access denied - admin role required",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during cleanup",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = DataCleanupResponse.class)
            )
        )
    })
    @PostMapping(
        value = "/delete-old-season-data",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<DataCleanupResponse> deleteOldSeasonData(
            @Parameter(
                name = "request",
                description = "Data cleanup request parameters",
                required = true,
                schema = @Schema(implementation = DataCleanupRequest.class)
            )
            @Valid @RequestBody DataCleanupRequest request) {

        log.info("Received data cleanup request: dryRun={}, tables={}, chunkSize={}", 
                request.isDryRun(), request.getTables(), request.getChunkSize());

        try {
            // Validate request parameters
            request.validate();

            // Log security information
            log.info("Data cleanup initiated by admin user");

            // Perform the cleanup operation
            DataCleanupResponse response = cleanupService.cleanupData(request);

            // Log the results
            if (response.isSuccess()) {
                log.info("Data cleanup completed successfully: {}", response.getSummary());
                return ResponseEntity.ok(response);
            } else {
                log.warn("Data cleanup completed with errors: {}", response.getMessage());
                return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT).body(response);
            }

        } catch (IllegalArgumentException e) {
            log.error("Invalid request parameters: {}", e.getMessage());
            DataCleanupResponse errorResponse = DataCleanupResponse.error("Invalid request: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);

        } catch (SecurityException e) {
            log.error("Security error during data cleanup: {}", e.getMessage());
            DataCleanupResponse errorResponse = DataCleanupResponse.error("Access denied: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);

        } catch (Exception e) {
            log.error("Unexpected error during data cleanup", e);
            DataCleanupResponse errorResponse = DataCleanupResponse.error("Internal server error: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Endpoint to get information about supported tables and default parameters.
     * 
     * @return Information about the cleanup operation
     */
    @Operation(
        summary = "Get data cleanup information",
        description = "Returns information about supported tables, default parameters, and operation details."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Information retrieved successfully",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Access denied - admin role required",
            content = @Content
        )
    })
    @GetMapping(
        value = "/delete-old-season-data/info",
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<DataCleanupInfoResponse> getDataCleanupInfo() {
        log.info("Data cleanup info requested");

        DataCleanupInfoResponse info = new DataCleanupInfoResponse();
        info.setSupportedTables(DataCleanupRequest.getSupportedTables());
        info.setDefaultTables(DataCleanupRequest.getDefaultTables());
        info.setDefaultChunkSize(10000);
        info.setMinChunkSize(1000);
        info.setMaxChunkSize(50000);
        info.setDefaultDelayMs(50);
        info.setMaxDelayMs(5000);
        info.setDescription("Data cleanup operation deletes all records from specified tables in dependency order");

        return ResponseEntity.ok(info);
    }

    /**
     * Response DTO for cleanup information endpoint.
     */
    @Schema(description = "Information about data cleanup operation")
    public static class DataCleanupInfoResponse {
        @Schema(description = "List of all supported table names")
        private java.util.List<String> supportedTables;

        @Schema(description = "Default list of tables processed in dependency order")
        private java.util.List<String> defaultTables;

        @Schema(description = "Default chunk size for batch processing")
        private int defaultChunkSize;

        @Schema(description = "Minimum allowed chunk size")
        private int minChunkSize;

        @Schema(description = "Maximum allowed chunk size")
        private int maxChunkSize;

        @Schema(description = "Default delay between batches in milliseconds")
        private long defaultDelayMs;

        @Schema(description = "Maximum allowed delay between batches in milliseconds")
        private long maxDelayMs;

        @Schema(description = "Description of the cleanup operation")
        private String description;

        // Getters and setters
        public java.util.List<String> getSupportedTables() { return supportedTables; }
        public void setSupportedTables(java.util.List<String> supportedTables) { this.supportedTables = supportedTables; }

        public java.util.List<String> getDefaultTables() { return defaultTables; }
        public void setDefaultTables(java.util.List<String> defaultTables) { this.defaultTables = defaultTables; }

        public int getDefaultChunkSize() { return defaultChunkSize; }
        public void setDefaultChunkSize(int defaultChunkSize) { this.defaultChunkSize = defaultChunkSize; }

        public int getMinChunkSize() { return minChunkSize; }
        public void setMinChunkSize(int minChunkSize) { this.minChunkSize = minChunkSize; }

        public int getMaxChunkSize() { return maxChunkSize; }
        public void setMaxChunkSize(int maxChunkSize) { this.maxChunkSize = maxChunkSize; }

        public long getDefaultDelayMs() { return defaultDelayMs; }
        public void setDefaultDelayMs(long defaultDelayMs) { this.defaultDelayMs = defaultDelayMs; }

        public long getMaxDelayMs() { return maxDelayMs; }
        public void setMaxDelayMs(long maxDelayMs) { this.maxDelayMs = maxDelayMs; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
}
