# Data Cleanup Feature - Usage Guide

## Overview

The Data Cleanup feature provides a safe and efficient way to delete old season data from the HBL Handball Manager database. It uses optimized native SQL batch processing to achieve 10x+ better performance compared to standard JPA deletion methods.

## Key Features

- **High Performance**: Native SQL batch deletion (~10,000+ records/second vs ~1,000 with JPA)
- **Safety First**: Dry-run mode for previewing deletions before execution
- **Dependency-Aware**: Automatically handles foreign key constraints by deleting in correct order
- **Configurable**: Customizable batch sizes, delays, and table selection
- **Admin-Only**: Secure endpoint requiring admin role authentication
- **Comprehensive Logging**: Detailed progress tracking and error reporting

## Supported Tables

The following tables can be cleaned up (listed in deletion order):

1. **transfer_market_bid** - Transfer market bids (typically largest table)
2. **transfer_market** - Transfer market offers
3. **lineup** - Team lineups for rounds
4. **user_round_score** - User scores per round
5. **team** - User team compositions
6. **league_invitation** - League invitations
7. **user_notification** - User notifications
8. **scheduler_job** - Scheduled job records

## API Endpoints

### Main Cleanup Endpoint

```
POST /admin/delete-old-season-data
```

**Security**: Requires `ADMIN` role

### Information Endpoint

```
GET /admin/delete-old-season-data/info
```

Returns supported tables, parameter limits, and operation details.

## Request Parameters

| Parameter | Type | Default | Range | Description |
|-----------|------|---------|-------|-------------|
| `dryRun` | boolean | `true` | - | Preview mode (no actual deletions) |
| `chunkSize` | integer | `10000` | 1000-50000 | Batch size for processing |
| `delayMs` | long | `50` | 0-5000 | Delay between batches (ms) |
| `tables` | array | all tables | supported tables | Tables to process |

## Usage Examples

### 1. Preview Mode (Dry Run)

**Recommended first step** - Preview what will be deleted:

```bash
curl -X POST "https://api.example.com/admin/delete-old-season-data" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dryRun": true,
    "chunkSize": 10000,
    "tables": ["transfer_market_bid", "user_notification"]
  }'
```

**Response:**
```json
{
  "success": true,
  "dryRun": true,
  "message": "Data cleanup preview completed successfully",
  "deletedRecords": {
    "transfer_market_bid": 4250000,
    "user_notification": 12500
  },
  "totalDeleted": 4262500,
  "executionTime": "00:00:05",
  "tablesProcessed": 2
}
```

### 2. Actual Deletion - Small Tables

Start with smaller tables for safety:

```bash
curl -X POST "https://api.example.com/admin/delete-old-season-data" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dryRun": false,
    "chunkSize": 10000,
    "delayMs": 50,
    "tables": ["scheduler_job", "league_invitation"]
  }'
```

### 3. Large Table Cleanup

For large tables, use appropriate batch sizes and delays:

```bash
curl -X POST "https://api.example.com/admin/delete-old-season-data" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dryRun": false,
    "chunkSize": 20000,
    "delayMs": 100,
    "tables": ["transfer_market_bid", "user_round_score"]
  }'
```

### 4. Complete Cleanup

Delete all supported tables:

```bash
curl -X POST "https://api.example.com/admin/delete-old-season-data" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "dryRun": false,
    "chunkSize": 15000,
    "delayMs": 75
  }'
```

## Response Format

### Success Response

```json
{
  "success": true,
  "dryRun": false,
  "message": "Data cleanup completed successfully",
  "deletedRecords": {
    "transfer_market_bid": 4250000,
    "transfer_market": 85000,
    "lineup": 28000,
    "user_round_score": 35000,
    "team": 42000,
    "league_invitation": 450,
    "user_notification": 12500,
    "scheduler_job": 120
  },
  "totalDeleted": 4453070,
  "executionTime": "00:25:30",
  "startTime": "2024-01-15T10:00:00",
  "endTime": "2024-01-15T10:25:30",
  "tablesProcessed": 8,
  "errors": {}
}
```

### Error Response

```json
{
  "success": false,
  "message": "Data cleanup completed with some errors",
  "deletedRecords": {
    "user_notification": 12500
  },
  "totalDeleted": 12500,
  "executionTime": "00:02:15",
  "tablesProcessed": 1,
  "errors": {
    "transfer_market_bid": "Database connection timeout"
  }
}
```

## Best Practices

### 1. Safety Protocol

1. **Always start with dry-run** to preview deletions
2. **Test on staging environment** first
3. **Create database backup** before large deletions
4. **Start with small tables** to verify the process
5. **Monitor system performance** during execution

### 2. Performance Optimization

| Table Size | Recommended Chunk Size | Delay (ms) |
|------------|----------------------|------------|
| < 100K records | 5,000 | 25 |
| 100K - 1M records | 10,000 | 50 |
| 1M - 10M records | 20,000 | 100 |
| > 10M records | 30,000 | 150 |

### 3. Scheduling Recommendations

- **Run during low-traffic periods** (e.g., 2-4 AM)
- **Monitor database performance** during execution
- **Use smaller batches** if system load is high
- **Increase delays** if database shows stress

## Error Handling

### Common Error Scenarios

1. **Database Connection Issues**
   - Retry with smaller batch sizes
   - Increase delays between batches

2. **Foreign Key Constraint Violations**
   - Should not occur (deletion order is handled automatically)
   - Contact development team if this happens

3. **Timeout Errors**
   - Reduce batch size
   - Increase delay between batches

4. **Memory Issues**
   - Reduce batch size significantly
   - Monitor system resources

### Recovery Procedures

1. **Partial Completion**: The operation can be resumed by running again with remaining tables
2. **Error Logging**: Check application logs for detailed error information
3. **Database Monitoring**: Monitor database performance metrics during and after cleanup

## Monitoring and Logging

### Application Logs

The cleanup operation provides detailed logging:

```
INFO  - Starting data cleanup with request: dryRun=false, tables=[transfer_market_bid], chunkSize=10000
INFO  - Batch 1: Deleted 10000 rows from 'hm.transfer_market_bid' in 1250ms (total: 10000)
INFO  - Batch 2: Deleted 10000 rows from 'hm.transfer_market_bid' in 1180ms (total: 20000)
INFO  - Completed batch deletion from hm.transfer_market_bid. Total rows deleted: 4250000
INFO  - Data cleanup completed. Total deleted: 4250000, Execution time: 1520000ms
```

### Database Monitoring

Monitor these metrics during cleanup:
- CPU usage
- Memory consumption
- Database connection pool
- Query execution times
- Lock wait times

## Security

- **Admin Role Required**: Only users with `ADMIN` role can access the endpoint
- **Authentication**: Requires valid JWT token with admin privileges
- **Audit Logging**: All cleanup operations are logged with user information
- **Rate Limiting**: Consider implementing rate limiting for additional security

## Troubleshooting

### Issue: "Chunk size must be between 1000 and 50000"
**Solution**: Adjust the `chunkSize` parameter to be within the valid range.

### Issue: "Unsupported tables: [table_name]"
**Solution**: Use only supported table names. Check `/info` endpoint for valid tables.

### Issue: "Access denied - admin role required"
**Solution**: Ensure the user has admin role and valid authentication token.

### Issue: Slow performance
**Solutions**:
- Reduce batch size
- Increase delay between batches
- Run during low-traffic periods
- Check database performance metrics

### Issue: Database connection timeouts
**Solutions**:
- Reduce batch size significantly
- Increase delay between batches
- Check database connection pool settings
- Monitor database load

## Support

For issues or questions regarding the data cleanup feature:
1. Check application logs for detailed error information
2. Review database performance metrics
3. Contact the development team with specific error messages and system metrics
