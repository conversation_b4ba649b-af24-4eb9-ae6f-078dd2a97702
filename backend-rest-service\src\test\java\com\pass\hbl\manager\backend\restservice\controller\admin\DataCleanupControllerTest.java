package com.pass.hbl.manager.backend.restservice.controller.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupRequest;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupResponse;
import com.pass.hbl.manager.backend.persistence.service.admin.OldSeasonDataCleanupService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(DataCleanupController.class)
class DataCleanupControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private OldSeasonDataCleanupService cleanupService;

    private DataCleanupRequest validRequest;
    private DataCleanupResponse successResponse;

    @BeforeEach
    void setUp() {
        validRequest = new DataCleanupRequest();
        validRequest.setDryRun(true);
        validRequest.setChunkSize(10000);
        validRequest.setDelayMs(50);
        validRequest.setTables(Arrays.asList("transfer_market_bid", "user_notification"));

        Map<String, Long> deletedRecords = new HashMap<>();
        deletedRecords.put("transfer_market_bid", 1000000L);
        deletedRecords.put("user_notification", 25000L);

        successResponse = DataCleanupResponse.success(
            "Data cleanup preview completed successfully",
            deletedRecords,
            "00:05:30",
            true
        );
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteOldSeasonData_Success() throws Exception {
        // Arrange
        when(cleanupService.cleanupData(any(DataCleanupRequest.class))).thenReturn(successResponse);

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.dryRun").value(true))
                .andExpect(jsonPath("$.totalDeleted").value(1025000))
                .andExpect(jsonPath("$.tablesProcessed").value(2))
                .andExpect(jsonPath("$.executionTime").value("00:05:30"))
                .andExpect(jsonPath("$.message").value("Data cleanup preview completed successfully"));

        verify(cleanupService).cleanupData(any(DataCleanupRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteOldSeasonData_ActualDeletion() throws Exception {
        // Arrange
        validRequest.setDryRun(false);
        successResponse.setDryRun(false);
        successResponse.setMessage("Data cleanup completed successfully");

        when(cleanupService.cleanupData(any(DataCleanupRequest.class))).thenReturn(successResponse);

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.dryRun").value(false))
                .andExpect(jsonPath("$.message").value("Data cleanup completed successfully"));

        verify(cleanupService).cleanupData(any(DataCleanupRequest.class));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteOldSeasonData_WithErrors() throws Exception {
        // Arrange
        Map<String, String> errors = new HashMap<>();
        errors.put("transfer_market_bid", "Database connection failed");
        
        DataCleanupResponse errorResponse = DataCleanupResponse.success(
            "Data cleanup completed with some errors",
            Map.of("user_notification", 25000L),
            "00:02:15",
            false
        );
        errorResponse.setErrors(errors);

        when(cleanupService.cleanupData(any(DataCleanupRequest.class))).thenReturn(errorResponse);

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isPartialContent())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Data cleanup completed with some errors"))
                .andExpect(jsonPath("$.errors.transfer_market_bid").value("Database connection failed"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteOldSeasonData_InvalidRequest() throws Exception {
        // Arrange
        validRequest.setChunkSize(0); // Invalid chunk size

        when(cleanupService.cleanupData(any(DataCleanupRequest.class)))
            .thenThrow(new IllegalArgumentException("Chunk size must be between 1000 and 50000"));

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Invalid request: Chunk size must be between 1000 and 50000"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteOldSeasonData_ServiceException() throws Exception {
        // Arrange
        when(cleanupService.cleanupData(any(DataCleanupRequest.class)))
            .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("Internal server error: Database connection failed"));
    }

    @Test
    void testDeleteOldSeasonData_NoAuthentication() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isUnauthorized());

        verifyNoInteractions(cleanupService);
    }

    @Test
    @WithMockUser(roles = "USER") // Wrong role
    void testDeleteOldSeasonData_InsufficientPermissions() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isForbidden());

        verifyNoInteractions(cleanupService);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteOldSeasonData_InvalidJson() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("{ invalid json }"))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(cleanupService);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteOldSeasonData_MissingRequestBody() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(cleanupService);
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testGetDataCleanupInfo_Success() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/delete-old-season-data/info"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.supportedTables").isArray())
                .andExpect(jsonPath("$.supportedTables.length()").value(8))
                .andExpect(jsonPath("$.defaultTables").isArray())
                .andExpect(jsonPath("$.defaultChunkSize").value(10000))
                .andExpect(jsonPath("$.minChunkSize").value(1000))
                .andExpect(jsonPath("$.maxChunkSize").value(50000))
                .andExpect(jsonPath("$.defaultDelayMs").value(50))
                .andExpect(jsonPath("$.maxDelayMs").value(5000))
                .andExpect(jsonPath("$.description").exists());

        verifyNoInteractions(cleanupService);
    }

    @Test
    void testGetDataCleanupInfo_NoAuthentication() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/delete-old-season-data/info"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void testGetDataCleanupInfo_InsufficientPermissions() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/delete-old-season-data/info"))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDeleteOldSeasonData_CustomParameters() throws Exception {
        // Arrange
        DataCleanupRequest customRequest = new DataCleanupRequest();
        customRequest.setDryRun(false);
        customRequest.setChunkSize(5000);
        customRequest.setDelayMs(100);
        customRequest.setTables(Arrays.asList("scheduler_job"));

        DataCleanupResponse customResponse = DataCleanupResponse.success(
            "Data cleanup completed successfully",
            Map.of("scheduler_job", 500L),
            "00:01:15",
            false
        );

        when(cleanupService.cleanupData(any(DataCleanupRequest.class))).thenReturn(customResponse);

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(customRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.totalDeleted").value(500))
                .andExpect(jsonPath("$.tablesProcessed").value(1));

        verify(cleanupService).cleanupData(any(DataCleanupRequest.class));
    }
}
