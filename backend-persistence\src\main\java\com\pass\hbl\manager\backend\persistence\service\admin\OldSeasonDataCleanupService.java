package com.pass.hbl.manager.backend.persistence.service.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupRequest;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupResponse;
import com.pass.hbl.manager.backend.persistence.repository.AbstractSchedulerJobRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.*;
import com.pass.hbl.manager.backend.persistence.util.BatchDeletionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Service for cleaning up old season data from the database.
 * Implements optimized batch deletion strategies for different table sizes.
 */
@Service
@Slf4j
public class OldSeasonDataCleanupService {

    @Autowired
    private HmTransferMarketBidRepository transferMarketBidRepository;

    @Autowired
    private HmTransferMarketRepository transferMarketRepository;

    @Autowired
    private HmLineupRepository lineupRepository;

    @Autowired
    private HmUserRoundScoreRepository userRoundScoreRepository;

    @Autowired
    private HmTeamRepository teamRepository;

    @Autowired
    private HmLeagueInvitationRepository leagueInvitationRepository;

    @Autowired
    private HmUserNotificationRepository userNotificationRepository;

    @Autowired
    private AbstractSchedulerJobRepository<?> schedulerJobRepository;

    @Autowired
    private BatchDeletionUtil batchDeletionUtil;

    /**
     * Main method to clean up data based on the request parameters.
     * 
     * @param request The cleanup request with parameters
     * @return DataCleanupResponse with results
     */
    @Transactional
    public DataCleanupResponse cleanupData(DataCleanupRequest request) {
        Objects.requireNonNull(request, "Request cannot be null");
        request.validate();

        log.info("Starting data cleanup with request: {}", request);
        
        Map<String, Long> deletedRecords = new HashMap<>();
        Map<String, String> errors = new HashMap<>();
        long startTime = System.currentTimeMillis();
        LocalDateTime operationStartTime = LocalDateTime.now();

        try {
            if (request.isDryRun()) {
                log.info("DRY RUN MODE - No actual deletions will be performed");
                deletedRecords = countRecordsToDelete(request.getTablesInDeletionOrder());
            } else {
                deletedRecords = performActualDeletion(request, errors);
            }

            long executionTime = System.currentTimeMillis() - startTime;
            long totalDeleted = deletedRecords.values().stream().mapToLong(Long::longValue).sum();

            log.info("Data cleanup completed. Total {}: {}, Execution time: {}ms", 
                    request.isDryRun() ? "counted" : "deleted", totalDeleted, executionTime);

            DataCleanupResponse response = DataCleanupResponse.success(
                request.isDryRun() ? "Data cleanup preview completed successfully" : "Data cleanup completed successfully",
                deletedRecords,
                formatExecutionTime(executionTime),
                request.isDryRun()
            );
            
            response.setStartTime(operationStartTime);
            response.setEndTime(LocalDateTime.now());
            
            if (!errors.isEmpty()) {
                response.setErrors(errors);
                response.setMessage("Data cleanup completed with some errors");
            }

            return response;

        } catch (Exception e) {
            log.error("Error during data cleanup", e);
            long executionTime = System.currentTimeMillis() - startTime;
            
            DataCleanupResponse response = DataCleanupResponse.error("Data cleanup failed: " + e.getMessage());
            response.setStartTime(operationStartTime);
            response.setEndTime(LocalDateTime.now());
            response.setExecutionTime(formatExecutionTime(executionTime));
            response.setDeletedRecords(deletedRecords);
            response.updateTotalDeleted();
            response.updateTablesProcessed();
            
            return response;
        }
    }

    /**
     * Performs the actual deletion of records.
     * 
     * @param request The cleanup request
     * @param errors Map to collect any errors
     * @return Map of table names to deleted record counts
     */
    private Map<String, Long> performActualDeletion(DataCleanupRequest request, Map<String, String> errors) {
        Map<String, Long> deletedRecords = new HashMap<>();
        
        // Delete in dependency order to avoid foreign key constraint violations
        for (String tableName : request.getTablesInDeletionOrder()) {
            try {
                long deleted = deleteFromTable(tableName, request.getChunkSize(), request.getDelayMs());
                deletedRecords.put(tableName, deleted);
                log.info("Successfully deleted {} records from table '{}'", deleted, tableName);
            } catch (Exception e) {
                log.error("Error deleting from table '{}': {}", tableName, e.getMessage(), e);
                errors.put(tableName, e.getMessage());
                deletedRecords.put(tableName, 0L);
            }
        }
        
        return deletedRecords;
    }

    /**
     * Counts records that would be deleted for dry run mode.
     * 
     * @param tables List of table names to count
     * @return Map of table names to record counts
     */
    private Map<String, Long> countRecordsToDelete(java.util.List<String> tables) {
        Map<String, Long> counts = new HashMap<>();

        for (String tableName : tables) {
            try {
                long count = countRecordsInTable(tableName);
                counts.put(tableName, count);
                log.info("Table '{}' contains {} records", tableName, count);
            } catch (Exception e) {
                log.error("Error counting records in table '{}': {}", tableName, e.getMessage(), e);
                counts.put(tableName, 0L);
            }
        }

        return counts;
    }

    /**
     * Deletes records from a specific table using the appropriate strategy.
     * 
     * @param tableName The table to delete from
     * @param chunkSize Batch size for deletion
     * @param delayMs Delay between batches
     * @return Number of records deleted
     */
    private long deleteFromTable(String tableName, int chunkSize, long delayMs) {
        return switch (tableName) {
            case "transfer_market_bid" -> deleteTransferMarketBids(chunkSize, delayMs);
            case "transfer_market" -> deleteTransferMarkets(chunkSize, delayMs);
            case "lineup" -> deleteLineups(chunkSize, delayMs);
            case "user_round_score" -> deleteUserRoundScores(chunkSize, delayMs);
            case "team" -> deleteTeams(chunkSize, delayMs);
            case "league_invitation" -> deleteLeagueInvitations();
            case "user_notification" -> deleteUserNotifications(chunkSize, delayMs);
            case "scheduler_job" -> deleteSchedulerJobs();
            default -> throw new IllegalArgumentException("Unsupported table: " + tableName);
        };
    }

    /**
     * Counts records in a specific table.
     * 
     * @param tableName The table to count
     * @return Number of records
     */
    private long countRecordsInTable(String tableName) {
        return switch (tableName) {
            case "transfer_market_bid" -> transferMarketBidRepository.countAllTransferMarketBids();
            case "transfer_market" -> transferMarketRepository.countAllTransferMarkets();
            case "lineup" -> lineupRepository.countAllLineups();
            case "user_round_score" -> userRoundScoreRepository.countAllUserRoundScores();
            case "team" -> teamRepository.countAllTeams();
            case "league_invitation" -> leagueInvitationRepository.count();
            case "user_notification" -> userNotificationRepository.countAllUserNotifications();
            case "scheduler_job" -> schedulerJobRepository.count();
            default -> throw new IllegalArgumentException("Unsupported table: " + tableName);
        };
    }

    // Deletion methods for each table
    private long deleteTransferMarketBids(int chunkSize, long delayMs) {
        log.info("Deleting transfer market bids using optimized batch deletion");
        return batchDeletionUtil.deleteInBatches("hm.transfer_market_bid", chunkSize, delayMs);
    }

    private long deleteTransferMarkets(int chunkSize, long delayMs) {
        log.info("Deleting transfer markets using optimized batch deletion");
        return batchDeletionUtil.deleteInBatches("hm.transfer_market", chunkSize, delayMs);
    }

    private long deleteLineups(int chunkSize, long delayMs) {
        log.info("Deleting lineups using optimized batch deletion");
        return batchDeletionUtil.deleteInBatches("hm.lineup", chunkSize, delayMs);
    }

    private long deleteUserRoundScores(int chunkSize, long delayMs) {
        log.info("Deleting user round scores using optimized batch deletion");
        return batchDeletionUtil.deleteInBatches("hm.user_round_score", chunkSize, delayMs);
    }

    private long deleteTeams(int chunkSize, long delayMs) {
        log.info("Deleting teams using optimized batch deletion");
        return batchDeletionUtil.deleteInBatches("hm.team", chunkSize, delayMs);
    }

    private long deleteLeagueInvitations() {
        log.info("Deleting league invitations using simple deleteAll");
        long count = leagueInvitationRepository.count();
        leagueInvitationRepository.deleteAll();
        return count;
    }

    private long deleteUserNotifications(int chunkSize, long delayMs) {
        log.info("Deleting user notifications using optimized batch deletion");
        return batchDeletionUtil.deleteInBatches("hm.user_notification", chunkSize, delayMs);
    }

    private long deleteSchedulerJobs() {
        log.info("Deleting scheduler jobs using simple deleteAll");
        long count = schedulerJobRepository.count();
        schedulerJobRepository.deleteAll();
        return count;
    }

    /**
     * Formats execution time from milliseconds to HH:mm:ss format.
     * 
     * @param milliseconds Execution time in milliseconds
     * @return Formatted time string
     */
    private String formatExecutionTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60);
        } else {
            return String.format("%02d:%02d", minutes, seconds % 60);
        }
    }
}
