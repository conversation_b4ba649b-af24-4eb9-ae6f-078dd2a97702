package com.pass.hbl.manager.backend.persistence.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BatchDeletionUtilTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private BatchDeletionUtil batchDeletionUtil;

    @BeforeEach
    void setUp() {
        // Reset mocks before each test
        reset(jdbcTemplate);
    }

    @Test
    void testDeleteInBatches_Success() {
        // Arrange
        String tableName = "test_table";
        int chunkSize = 1000;
        
        // Mock the deletion to return decreasing numbers until 0
        when(jdbcTemplate.update(anyString(), eq(chunkSize)))
            .thenReturn(1000)  // First batch
            .thenReturn(500)   // Second batch
            .thenReturn(0);    // No more records

        // Act
        long result = batchDeletionUtil.deleteInBatches(tableName, chunkSize);

        // Assert
        assertEquals(1500, result);
        verify(jdbcTemplate, times(3)).update("DELETE FROM test_table LIMIT ?", chunkSize);
    }

    @Test
    void testDeleteInBatches_WithDelay() {
        // Arrange
        String tableName = "test_table";
        int chunkSize = 1000;
        long delayMs = 100;
        
        when(jdbcTemplate.update(anyString(), eq(chunkSize)))
            .thenReturn(1000)
            .thenReturn(0);

        long startTime = System.currentTimeMillis();

        // Act
        long result = batchDeletionUtil.deleteInBatches(tableName, chunkSize, delayMs);

        // Assert
        long duration = System.currentTimeMillis() - startTime;
        assertEquals(1000, result);
        assertTrue(duration >= delayMs, "Should have waited for the delay");
        verify(jdbcTemplate, times(2)).update("DELETE FROM test_table LIMIT ?", chunkSize);
    }

    @Test
    void testDeleteInBatches_NullTableName() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> 
            batchDeletionUtil.deleteInBatches(null, 1000));
    }

    @Test
    void testDeleteInBatches_InvalidChunkSize() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
            batchDeletionUtil.deleteInBatches("test_table", 0));
        
        assertThrows(IllegalArgumentException.class, () -> 
            batchDeletionUtil.deleteInBatches("test_table", -1));
    }

    @Test
    void testDeleteInBatches_InvalidDelay() {
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
            batchDeletionUtil.deleteInBatches("test_table", 1000, -1));
    }

    @Test
    void testDeleteInBatchesWithCondition_Success() {
        // Arrange
        String tableName = "test_table";
        String whereClause = "created_at < '2023-01-01'";
        int chunkSize = 1000;
        
        when(jdbcTemplate.update(anyString(), eq(chunkSize)))
            .thenReturn(500)
            .thenReturn(0);

        // Act
        long result = batchDeletionUtil.deleteInBatchesWithCondition(tableName, whereClause, chunkSize);

        // Assert
        assertEquals(500, result);
        verify(jdbcTemplate, times(2)).update(
            "DELETE FROM test_table WHERE created_at < '2023-01-01' LIMIT ?", 
            chunkSize
        );
    }

    @Test
    void testDeleteInBatchesWithCondition_NullWhereClause() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> 
            batchDeletionUtil.deleteInBatchesWithCondition("test_table", null, 1000));
    }

    @Test
    void testCountRecords_Success() {
        // Arrange
        String tableName = "test_table";
        when(jdbcTemplate.queryForObject("SELECT COUNT(*) FROM test_table", Long.class))
            .thenReturn(12345L);

        // Act
        long result = batchDeletionUtil.countRecords(tableName);

        // Assert
        assertEquals(12345L, result);
        verify(jdbcTemplate).queryForObject("SELECT COUNT(*) FROM test_table", Long.class);
    }

    @Test
    void testCountRecords_NullResult() {
        // Arrange
        String tableName = "test_table";
        when(jdbcTemplate.queryForObject("SELECT COUNT(*) FROM test_table", Long.class))
            .thenReturn(null);

        // Act
        long result = batchDeletionUtil.countRecords(tableName);

        // Assert
        assertEquals(0L, result);
    }

    @Test
    void testCountRecords_NullTableName() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> 
            batchDeletionUtil.countRecords(null));
    }

    @Test
    void testCountRecordsWithCondition_Success() {
        // Arrange
        String tableName = "test_table";
        String whereClause = "status = 'ACTIVE'";
        when(jdbcTemplate.queryForObject("SELECT COUNT(*) FROM test_table WHERE status = 'ACTIVE'", Long.class))
            .thenReturn(999L);

        // Act
        long result = batchDeletionUtil.countRecordsWithCondition(tableName, whereClause);

        // Assert
        assertEquals(999L, result);
        verify(jdbcTemplate).queryForObject("SELECT COUNT(*) FROM test_table WHERE status = 'ACTIVE'", Long.class);
    }

    @Test
    void testCountRecordsWithCondition_NullWhereClause() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> 
            batchDeletionUtil.countRecordsWithCondition("test_table", null));
    }

    @Test
    void testDeleteInBatches_DatabaseException() {
        // Arrange
        String tableName = "test_table";
        int chunkSize = 1000;
        
        when(jdbcTemplate.update(anyString(), eq(chunkSize)))
            .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> 
            batchDeletionUtil.deleteInBatches(tableName, chunkSize));
        
        assertEquals("Database connection failed", exception.getMessage());
    }

    @Test
    void testDeleteInBatches_InterruptedException() throws InterruptedException {
        // Arrange
        String tableName = "test_table";
        int chunkSize = 1000;
        long delayMs = 1000;
        
        when(jdbcTemplate.update(anyString(), eq(chunkSize)))
            .thenReturn(1000)
            .thenReturn(500);

        // Interrupt the current thread to simulate interruption
        Thread.currentThread().interrupt();

        // Act
        long result = batchDeletionUtil.deleteInBatches(tableName, chunkSize, delayMs);

        // Assert
        assertEquals(1000, result); // Should stop after first batch due to interruption
        assertTrue(Thread.currentThread().isInterrupted());
        
        // Clear the interrupted status
        Thread.interrupted();
    }

    @Test
    void testDeleteInBatches_EmptyTable() {
        // Arrange
        String tableName = "empty_table";
        int chunkSize = 1000;
        
        when(jdbcTemplate.update(anyString(), eq(chunkSize)))
            .thenReturn(0); // No records to delete

        // Act
        long result = batchDeletionUtil.deleteInBatches(tableName, chunkSize);

        // Assert
        assertEquals(0, result);
        verify(jdbcTemplate, times(1)).update("DELETE FROM empty_table LIMIT ?", chunkSize);
    }

    @Test
    void testDeleteInBatches_LargeDataset() {
        // Arrange
        String tableName = "large_table";
        int chunkSize = 10000;
        
        // Simulate deleting a large dataset in multiple batches
        when(jdbcTemplate.update(anyString(), eq(chunkSize)))
            .thenReturn(10000)  // Batch 1
            .thenReturn(10000)  // Batch 2
            .thenReturn(10000)  // Batch 3
            .thenReturn(5000)   // Batch 4 (partial)
            .thenReturn(0);     // No more records

        // Act
        long result = batchDeletionUtil.deleteInBatches(tableName, chunkSize);

        // Assert
        assertEquals(35000, result);
        verify(jdbcTemplate, times(5)).update("DELETE FROM large_table LIMIT ?", chunkSize);
    }
}
