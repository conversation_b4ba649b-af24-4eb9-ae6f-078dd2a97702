package com.pass.hbl.manager.backend.persistence.repository.hm;

import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.hm.HmTransferMarket;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface HmTransferMarketRepository extends PagingAndSortingRepository<HmTransferMarket, UUID> {

    Iterable<HmTransferMarket> findByLeagueIdAndOwnerId(UUID leagueId, UUID ownerId);

    Iterable<HmTransferMarketDO> findByLeagueIdAndOwnerIdAndAuctionEndIsBefore(UUID leagueId, UUID ownerId, LocalDateTime auctionEnd);

    long countByLeagueIdAndOwnerId(UUID leagueId, UUID ownerId);

    Iterable<HmTransferMarket> findByLeagueId(UUID leagueId);

    Iterable<HmTransferMarket> findByOwnerId(UUID ownerId);

    Optional<HmTransferMarket> findByLeagueIdAndPlayerId(UUID leagueId, UUID playerId);

    @Query("select t.league.season.id from HmTransferMarket t where t.id = :id and t.deleted = false")
    Optional<UUID> findSeasonIdByTransferMarketId(@Param("id") UUID id);

    @Query("select t.league.id as id,  t.league.season.id as seasonId, t.league.isActive as isActive from HmTransferMarket t where t.id = :id and t.deleted = false")
    Optional<HmLeagueSeasonInfoDO> findLeagueSeasonInfoByTransferMarketId(@Param("id") UUID id);

    @Query("select t.player.id from HmTransferMarket t where t.league.id = :leagueId and t.deleted = false")
    List<UUID> findPlayerIdsByLeagueId(UUID leagueId);

    @Query("select t.owner.id from HmTransferMarket  t where t.id = :id and t.deleted = false")
    Optional<UUID> findOwnerId(@Param("id") UUID id);

    @Query(value = """
            SELECT bid FROM hm.transfer_market as t inner join hm.transfer_market_bid as b
            on t.id = b.transfer_market_id
            WHERE t.league_id = :leagueId\s
            and t.player_id = :playerId\s
            and t.deleted = true\s
            and b.status = 'ACCEPTED'
            and b.deleted = true
            ORDER BY t.deleted_at DESC limit 1""", nativeQuery = true)
    Optional<Object> findLastPlayerTransferValueInLeague(UUID leagueId, UUID playerId);

    @Query(value = "SELECT * FROM hm.transfer_market t WHERE t.id = :id and deleted = true ORDER BY t.created_at DESC limit 1", nativeQuery = true)
    Optional<HmTransferMarket> findEndedTransferById(UUID id);

    @Query(value = "SELECT * FROM hm.transfer_market t left outer join hm.transfer_market_bid b on t.id = b.transfer_market_id " +
            "WHERE t.league_id = :leagueId and t.deleted = true and b.status = 'ACCEPTED' ORDER BY t.deleted_at DESC limit :count", nativeQuery = true)
    Iterable<HmTransferMarket> findLastTransfersByLeagueId(UUID leagueId, int count);

    @Query(value = "SELECT  Cast(t.id as varchar) as id, Cast(t.league_id as varchar) as leagueId, t.deleted_at as deletedAt FROM hm.transfer_market t left outer join hm.transfer_market_bid b on t.id = b.transfer_market_id " +
            "WHERE t.player_id = :playerId and t.deleted = true and t.deleted_at >= :dateFrom and t.deleted_at <= :dateTo and b.status = 'ACCEPTED'", nativeQuery = true)
    Iterable<HmEndedTransferDO> findEndedPlayerTransfersInDateInterval(UUID playerId, LocalDateTime dateFrom, LocalDateTime dateTo);

    @Query(value = "SELECT Cast(league_id as varchar) as leagueId, count(*) as expiredTransfers from hm.transfer_market where league_id in :leagueIds and deleted = false and owner_id = :ownerId and auction_end < now() GROUP BY league_id", nativeQuery = true)
    List<HmLeagueExpiredTransfersDO> findLeagueExpiredTransfersInfoList(List<UUID> leagueIds, UUID ownerId);

    @Query("select t.id, t.owner.id, t.league.id, t.player.id, t.price, t.auctionEnd from HmTransferMarket t where t.deleted = false and t.id = :id")
    List<Object[]> findTransferMarketInfoById(@Param("id") UUID id);

    @Modifying
    @Query("update HmTransferMarket t set t.deleted = true, t.deletedAt = CURRENT_TIMESTAMP where (t.league.id in :leagueIds and t.deleted = false)")
    int deleteByLeagueIdIn(@Param("leagueIds") List<UUID> leagueIds);

    @Query(value = """
            select count(distinct transfer_market_id) from hm.transfer_market_bid b inner join hm.transfer_market t
            on b.transfer_market_id = t.id
            where status = 'ACCEPTED' and b.deleted = true and t.deleted = true
            and t.league_id in (select id from hm.league where season_id = :seasonId and deleted = false)
            and (b.bidder_id = :userId or t.owner_id = :userId)
            """, nativeQuery = true)
    int countUserBuysAndSales(UUID userId, UUID seasonId);


    @Query(value = """
            select count(distinct transfer_market_id) from hm.transfer_market_bid b inner join hm.transfer_market t
            on b.transfer_market_id = t.id
            where status = 'ACCEPTED' and b.deleted = true and t.deleted = true
            and t.league_id in (select id from hm.league where id = :leagueId and deleted = false)
            and (b.bidder_id = :userId or t.owner_id = :userId)
            """, nativeQuery = true)
    int countUserBuysAndSalesInLeague(UUID userId, UUID leagueId);

    @Query(value = "SELECT * FROM hm.transfer_market t " +
            "WHERE t.league_id = :leagueId and t.owner_id = :userId and t.player_id = :playerId ORDER BY t.created_at DESC limit :count", nativeQuery = true)
    Iterable<HmTransferMarket> findLastTransfersByLeagueIdAndUserIdAndPlayerId(UUID leagueId, UUID userId, UUID playerId, int count);

    @Query(value = "SELECT Cast(id as varchar) as id, Cast(owner_id as varchar) as ownerId, Cast(player_id as varchar) as playerId, price, created_at as createdAt from hm.transfer_market where league_id = :leagueId and deleted = false order by created_at DESC limit :countNewTransfers", nativeQuery = true)
    List<HmTransferMarketLobbyDO> findNewTransfersByLeagueId(UUID leagueId, int countNewTransfers);

    @Query(value = """
            select p.first_name as playerFirstName, p.last_name as playerLastName, Cast(p.picture as varchar) as picture, b.bid , b.deleted_at as deletedAt from hm.transfer_market_bid b\s
            left outer join hm.transfer_market t on b.transfer_market_id = t.id
            left outer join hm.player p on t.player_id = p.id\s
            where bidder_id = :userId
            and transfer_market_id in (
            \tselect id from hm.transfer_market where\s
            \tleague_id = :leagueId
            ) and b.status = 'ACCEPTED' and b.deleted = true\s
            and b.deleted_at > :sinceDate\s
            """, nativeQuery = true)
    List<HmTransferMarketTransactionDO> findPurchasesByUserAndLeagueSinceDate(UUID userId, UUID leagueId, LocalDateTime sinceDate);

    @Query(value = """
            select p.first_name as playerFirstName, p.last_name as playerLastName, Cast(p.picture as varchar) as picture, b.bid , b.deleted_at as deletedAt from hm.transfer_market_bid b\s
            left outer join hm.transfer_market t on b.transfer_market_id = t.id
            left outer join hm.player p on t.player_id = p.id\s
            where b.transfer_market_id in (
            \tselect id from hm.transfer_market where owner_id = :userId
            \tand league_id = :leagueId
            \tand deleted = true
            ) and b.status = 'ACCEPTED' and b.deleted = true and b.deleted_at > :sinceDate
            """, nativeQuery = true)
    List<HmTransferMarketTransactionDO> findSalesByUserAndLeagueSinceDate(UUID userId, UUID leagueId, LocalDateTime sinceDate);

    @Query(value = "SELECT DISTINCT CAST(t.owner_id AS varchar) " +
            "FROM hm.transfer_market t " +
            "WHERE (t.created_at > :changedAfter OR t.deleted_at > :changedAfter) AND NOT t.owner_id = :excludedId",
            nativeQuery = true)
    List<String> findAllUsersWithTransferMarketActivityAfter(@Param("changedAfter") LocalDateTime changedAfter,@Param("excludedId") UUID excludedId);

    @Query(value = "SELECT DISTINCT Cast(t.owner_id as varchar) FROM hm.transfer_market t WHERE  t.created_at > :changedAfter AND NOT t.owner_id = :excludedId", nativeQuery = true)
    List<String> findAllUserWithPlayerSalesAfter(@Param("changedAfter") LocalDateTime changedAfter,@Param("excludedId") UUID excludedId);

    // Batch deletion methods for data cleanup
    @Modifying
    @Query(value = "DELETE FROM hm.transfer_market WHERE id IN (SELECT id FROM hm.transfer_market LIMIT :chunkSize)", nativeQuery = true)
    int deleteTransferMarketsBatch(@Param("chunkSize") int chunkSize);

    @Query(value = "SELECT COUNT(*) FROM hm.transfer_market", nativeQuery = true)
    long countAllTransferMarkets();
}
