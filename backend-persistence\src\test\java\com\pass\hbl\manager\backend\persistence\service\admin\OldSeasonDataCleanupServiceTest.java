package com.pass.hbl.manager.backend.persistence.service.admin;

import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupRequest;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupResponse;
import com.pass.hbl.manager.backend.persistence.repository.AbstractSchedulerJobRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.*;
import com.pass.hbl.manager.backend.persistence.util.BatchDeletionUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OldSeasonDataCleanupServiceTest {

    @Mock
    private HmTransferMarketBidRepository transferMarketBidRepository;

    @Mock
    private HmTransferMarketRepository transferMarketRepository;

    @Mock
    private HmLineupRepository lineupRepository;

    @Mock
    private HmUserRoundScoreRepository userRoundScoreRepository;

    @Mock
    private HmTeamRepository teamRepository;

    @Mock
    private HmLeagueInvitationRepository leagueInvitationRepository;

    @Mock
    private HmUserNotificationRepository userNotificationRepository;

    @Mock
    private AbstractSchedulerJobRepository<?> schedulerJobRepository;

    @Mock
    private BatchDeletionUtil batchDeletionUtil;

    @InjectMocks
    private OldSeasonDataCleanupService cleanupService;

    private DataCleanupRequest defaultRequest;

    @BeforeEach
    void setUp() {
        defaultRequest = new DataCleanupRequest();
        defaultRequest.setDryRun(false);
        defaultRequest.setChunkSize(10000);
        defaultRequest.setDelayMs(50);
        defaultRequest.setTables(DataCleanupRequest.getDefaultTables());
    }

    @Test
    void testCleanupData_DryRun_Success() {
        // Arrange
        defaultRequest.setDryRun(true);
        
        when(transferMarketBidRepository.countAllTransferMarketBids()).thenReturn(1000000L);
        when(transferMarketRepository.countAllTransferMarkets()).thenReturn(50000L);
        when(lineupRepository.countAllLineups()).thenReturn(200000L);
        when(userRoundScoreRepository.countAllUserRoundScores()).thenReturn(500000L);
        when(teamRepository.countAllTeams()).thenReturn(100000L);
        when(leagueInvitationRepository.count()).thenReturn(5000L);
        when(userNotificationRepository.countAllUserNotifications()).thenReturn(25000L);
        when(schedulerJobRepository.count()).thenReturn(1000L);

        // Act
        DataCleanupResponse response = cleanupService.cleanupData(defaultRequest);

        // Assert
        assertTrue(response.isSuccess());
        assertTrue(response.isDryRun());
        assertEquals(1881000L, response.getTotalDeleted());
        assertEquals(8, response.getTablesProcessed());
        assertTrue(response.getMessage().contains("preview completed successfully"));
        
        // Verify no actual deletions were performed
        verify(batchDeletionUtil, never()).deleteInBatches(anyString(), anyInt(), anyLong());
    }

    @Test
    void testCleanupData_ActualDeletion_Success() {
        // Arrange
        when(batchDeletionUtil.deleteInBatches("hm.transfer_market_bid", 10000, 50)).thenReturn(1000000L);
        when(batchDeletionUtil.deleteInBatches("hm.transfer_market", 10000, 50)).thenReturn(50000L);
        when(batchDeletionUtil.deleteInBatches("hm.lineup", 10000, 50)).thenReturn(200000L);
        when(batchDeletionUtil.deleteInBatches("hm.user_round_score", 10000, 50)).thenReturn(500000L);
        when(batchDeletionUtil.deleteInBatches("hm.team", 10000, 50)).thenReturn(100000L);
        when(batchDeletionUtil.deleteInBatches("hm.user_notification", 10000, 50)).thenReturn(25000L);
        
        when(leagueInvitationRepository.count()).thenReturn(5000L);
        when(schedulerJobRepository.count()).thenReturn(1000L);

        // Act
        DataCleanupResponse response = cleanupService.cleanupData(defaultRequest);

        // Assert
        assertTrue(response.isSuccess());
        assertFalse(response.isDryRun());
        assertEquals(1881000L, response.getTotalDeleted());
        assertEquals(8, response.getTablesProcessed());
        assertTrue(response.getMessage().contains("completed successfully"));
        
        // Verify all deletions were performed
        verify(batchDeletionUtil).deleteInBatches("hm.transfer_market_bid", 10000, 50);
        verify(batchDeletionUtil).deleteInBatches("hm.transfer_market", 10000, 50);
        verify(batchDeletionUtil).deleteInBatches("hm.lineup", 10000, 50);
        verify(batchDeletionUtil).deleteInBatches("hm.user_round_score", 10000, 50);
        verify(batchDeletionUtil).deleteInBatches("hm.team", 10000, 50);
        verify(batchDeletionUtil).deleteInBatches("hm.user_notification", 10000, 50);
        verify(leagueInvitationRepository).deleteAll();
        verify(schedulerJobRepository).deleteAll();
    }

    @Test
    void testCleanupData_PartialTableList() {
        // Arrange
        List<String> partialTables = Arrays.asList("transfer_market_bid", "user_notification");
        defaultRequest.setTables(partialTables);
        
        when(batchDeletionUtil.deleteInBatches("hm.transfer_market_bid", 10000, 50)).thenReturn(1000000L);
        when(batchDeletionUtil.deleteInBatches("hm.user_notification", 10000, 50)).thenReturn(25000L);

        // Act
        DataCleanupResponse response = cleanupService.cleanupData(defaultRequest);

        // Assert
        assertTrue(response.isSuccess());
        assertEquals(1025000L, response.getTotalDeleted());
        assertEquals(2, response.getTablesProcessed());
        
        // Verify only specified tables were processed
        verify(batchDeletionUtil).deleteInBatches("hm.transfer_market_bid", 10000, 50);
        verify(batchDeletionUtil).deleteInBatches("hm.user_notification", 10000, 50);
        verify(batchDeletionUtil, never()).deleteInBatches(eq("hm.transfer_market"), anyInt(), anyLong());
        verify(batchDeletionUtil, never()).deleteInBatches(eq("hm.lineup"), anyInt(), anyLong());
    }

    @Test
    void testCleanupData_WithErrors() {
        // Arrange
        when(batchDeletionUtil.deleteInBatches("hm.transfer_market_bid", 10000, 50))
            .thenThrow(new RuntimeException("Database connection failed"));
        when(batchDeletionUtil.deleteInBatches("hm.transfer_market", 10000, 50)).thenReturn(50000L);

        // Act
        DataCleanupResponse response = cleanupService.cleanupData(defaultRequest);

        // Assert
        assertTrue(response.isSuccess()); // Should still be successful overall
        assertTrue(response.hasErrors());
        assertEquals("Data cleanup completed with some errors", response.getMessage());
        assertTrue(response.getErrors().containsKey("transfer_market_bid"));
        assertEquals("Database connection failed", response.getErrors().get("transfer_market_bid"));
        
        // Should have processed other tables despite the error
        verify(batchDeletionUtil).deleteInBatches("hm.transfer_market", 10000, 50);
    }

    @Test
    void testCleanupData_NullRequest() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> 
            cleanupService.cleanupData(null));
    }

    @Test
    void testCleanupData_InvalidRequest() {
        // Arrange
        defaultRequest.setChunkSize(0); // Invalid chunk size

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
            cleanupService.cleanupData(defaultRequest));
    }

    @Test
    void testCleanupData_UnsupportedTable() {
        // Arrange
        defaultRequest.setTables(Arrays.asList("unsupported_table"));

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
            cleanupService.cleanupData(defaultRequest));
    }

    @Test
    void testCleanupData_EmptyTableList() {
        // Arrange
        defaultRequest.setTables(Arrays.asList());

        // Act
        DataCleanupResponse response = cleanupService.cleanupData(defaultRequest);

        // Assert
        assertTrue(response.isSuccess());
        assertEquals(0L, response.getTotalDeleted());
        assertEquals(0, response.getTablesProcessed());
    }

    @Test
    void testCleanupData_CustomChunkSizeAndDelay() {
        // Arrange
        defaultRequest.setChunkSize(5000);
        defaultRequest.setDelayMs(100);
        defaultRequest.setTables(Arrays.asList("transfer_market_bid"));
        
        when(batchDeletionUtil.deleteInBatches("hm.transfer_market_bid", 5000, 100)).thenReturn(500000L);

        // Act
        DataCleanupResponse response = cleanupService.cleanupData(defaultRequest);

        // Assert
        assertTrue(response.isSuccess());
        assertEquals(500000L, response.getTotalDeleted());
        
        // Verify custom parameters were used
        verify(batchDeletionUtil).deleteInBatches("hm.transfer_market_bid", 5000, 100);
    }

    @Test
    void testCleanupData_CriticalException() {
        // Arrange
        when(batchDeletionUtil.deleteInBatches(anyString(), anyInt(), anyLong()))
            .thenThrow(new OutOfMemoryError("Out of memory"));

        // Act
        DataCleanupResponse response = cleanupService.cleanupData(defaultRequest);

        // Assert
        assertFalse(response.isSuccess());
        assertTrue(response.getMessage().contains("Data cleanup failed"));
        assertTrue(response.getMessage().contains("Out of memory"));
        assertEquals(0L, response.getTotalDeleted());
    }

    @Test
    void testCleanupData_VerifyDeletionOrder() {
        // Arrange
        when(batchDeletionUtil.deleteInBatches(anyString(), anyInt(), anyLong())).thenReturn(1000L);
        when(leagueInvitationRepository.count()).thenReturn(100L);
        when(schedulerJobRepository.count()).thenReturn(50L);

        // Act
        cleanupService.cleanupData(defaultRequest);

        // Assert - verify deletion order by checking the order of calls
        var inOrder = inOrder(batchDeletionUtil, leagueInvitationRepository, schedulerJobRepository);
        
        inOrder.verify(batchDeletionUtil).deleteInBatches("hm.transfer_market_bid", 10000, 50);
        inOrder.verify(batchDeletionUtil).deleteInBatches("hm.transfer_market", 10000, 50);
        inOrder.verify(batchDeletionUtil).deleteInBatches("hm.lineup", 10000, 50);
        inOrder.verify(batchDeletionUtil).deleteInBatches("hm.user_round_score", 10000, 50);
        inOrder.verify(batchDeletionUtil).deleteInBatches("hm.team", 10000, 50);
        inOrder.verify(leagueInvitationRepository).deleteAll();
        inOrder.verify(batchDeletionUtil).deleteInBatches("hm.user_notification", 10000, 50);
        inOrder.verify(schedulerJobRepository).deleteAll();
    }
}
