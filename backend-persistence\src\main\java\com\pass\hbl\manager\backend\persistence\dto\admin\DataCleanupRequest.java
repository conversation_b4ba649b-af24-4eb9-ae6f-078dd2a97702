package com.pass.hbl.manager.backend.persistence.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * Request DTO for data cleanup operations.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request object for data cleanup operations")
public class DataCleanupRequest {

    @JsonProperty("dryRun")
    @Schema(description = "Whether to run in preview mode without actual deletions", example = "true")
    private boolean dryRun = true;

    @JsonProperty("chunkSize")
    @Min(value = 1000, message = "Chunk size must be at least 1000")
    @Max(value = 50000, message = "Chunk size must not exceed 50000")
    @Schema(description = "Batch size for processing large tables", example = "10000", minimum = "1000", maximum = "50000")
    private int chunkSize = 10000;

    @JsonProperty("delayMs")
    @Min(value = 0, message = "Delay must be non-negative")
    @Max(value = 5000, message = "Delay must not exceed 5000ms")
    @Schema(description = "Delay in milliseconds between batches to reduce database load", example = "50", minimum = "0", maximum = "5000")
    private long delayMs = 50;

    @JsonProperty("tables")
    @Schema(description = "List of tables to process (optional, defaults to all supported tables)")
    private List<String> tables = getDefaultTables();

    /**
     * Gets the default list of tables to clean up in the correct dependency order.
     * 
     * @return List of table names in deletion order
     */
    public static List<String> getDefaultTables() {
        return Arrays.asList(
            "transfer_market_bid",
            "transfer_market", 
            "lineup",
            "user_round_score",
            "team",
            "league_invitation",
            "user_notification",
            "scheduler_job"
        );
    }

    /**
     * Gets the supported table names.
     * 
     * @return List of all supported table names
     */
    public static List<String> getSupportedTables() {
        return getDefaultTables();
    }

    /**
     * Validates that all requested tables are supported.
     * 
     * @return true if all tables are supported, false otherwise
     */
    public boolean areAllTablesSupported() {
        if (tables == null || tables.isEmpty()) {
            return true;
        }
        return getSupportedTables().containsAll(tables);
    }

    /**
     * Gets the list of unsupported tables in the request.
     * 
     * @return List of unsupported table names
     */
    public List<String> getUnsupportedTables() {
        if (tables == null || tables.isEmpty()) {
            return List.of();
        }
        return tables.stream()
                .filter(table -> !getSupportedTables().contains(table))
                .toList();
    }

    /**
     * Validates the request parameters.
     * 
     * @throws IllegalArgumentException if validation fails
     */
    public void validate() {
        if (chunkSize < 1000 || chunkSize > 50000) {
            throw new IllegalArgumentException("Chunk size must be between 1000 and 50000");
        }
        
        if (delayMs < 0 || delayMs > 5000) {
            throw new IllegalArgumentException("Delay must be between 0 and 5000 milliseconds");
        }
        
        if (!areAllTablesSupported()) {
            throw new IllegalArgumentException("Unsupported tables: " + getUnsupportedTables());
        }
    }

    /**
     * Gets the tables to process, ensuring they are in the correct deletion order.
     * 
     * @return List of tables in dependency order
     */
    public List<String> getTablesInDeletionOrder() {
        if (tables == null || tables.isEmpty()) {
            return getDefaultTables();
        }
        
        // Filter and order the requested tables according to dependency order
        return getDefaultTables().stream()
                .filter(tables::contains)
                .toList();
    }
}
