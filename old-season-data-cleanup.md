# Old Season Data Cleanup Documentation

## Overview

This document outlines the implementation of the `/admin/delete-old-season-data` endpoint, which is designed to safely and efficiently delete all data from the specified tables in the HBL Handball Manager API.

## Tables to Clean Up

### Tables to be completely cleaned

The following tables will have all their data deleted:

1. **scheduler_job** - No dependencies
2. **league_invitation** - Depends on league
3. **user_notification** - Depends on user profile
4. **user_round_score** - Depends on user profile, league, and round
5. **lineup** - Depends on team and round
6. **transfer_market_bid** - Depends on transfer_market
7. **transfer_market** - Depends on user profile, league, and player
8. **team** - Depends on user profile, league, and player

## Deletion Order

Based on the foreign key constraints, the correct deletion order is:

1. **transfer_market_bid** (depends on transfer_market)
2. **transfer_market** (depends on user_profile, league, player)
3. **lineup** (depends on team, round)
4. **user_round_score** (depends on user_profile, league, round)
5. **team** (depends on user_profile, league, player)
6. **league_invitation** (depends on league)
7. **user_notification** (depends on user_profile)
8. **scheduler_job** (no dependencies)

## Implementation Approach

### 1. Service Layer

Create a dedicated service class `OldSeasonDataCleanupService` with the following components:

```java
@Service
@Slf4j
public class OldSeasonDataCleanupService {

    @Autowired
    private TransferMarketBidRepository transferMarketBidRepository;

    @Autowired
    private TransferMarketRepository transferMarketRepository;

    @Autowired
    private LineupRepository lineupRepository;

    @Autowired
    private UserRoundScoreRepository userRoundScoreRepository;

    @Autowired
    private TeamRepository teamRepository;

    @Autowired
    private LeagueInvitationRepository leagueInvitationRepository;

    @Autowired
    private UserNotificationRepository userNotificationRepository;

    @Autowired
    private SchedulerJobRepository schedulerJobRepository;

    @Transactional
    public void cleanupOldSeasonData() {
        log.info("Starting old season data cleanup");

        // Delete in dependency order
        deleteTransferMarketBids();
        deleteTransferMarkets();
        deleteLineups();
        deleteUserRoundScores();
        deleteTeams();
        deleteLeagueInvitations();
        deleteUserNotifications();
        deleteSchedulerJobs();

        log.info("Old season data cleanup completed");
    }

    private void deleteTransferMarketBids() {
        log.info("Deleting all transfer market bids");
        transferMarketBidRepository.deleteAll();
    }

    private void deleteTransferMarkets() {
        log.info("Deleting all transfer markets");
        transferMarketRepository.deleteAll();
    }

    private void deleteLineups() {
        log.info("Deleting all lineups");
        lineupRepository.deleteAll();
    }

    private void deleteUserRoundScores() {
        log.info("Deleting all user round scores");
        userRoundScoreRepository.deleteAll();
    }

    private void deleteTeams() {
        log.info("Deleting all teams");
        teamRepository.deleteAll();
    }

    private void deleteLeagueInvitations() {
        log.info("Deleting all league invitations");
        leagueInvitationRepository.deleteAll();
    }

    private void deleteUserNotifications() {
        log.info("Deleting all user notifications");
        userNotificationRepository.deleteAll();
    }

    private void deleteSchedulerJobs() {
        log.info("Deleting all scheduler jobs");
        schedulerJobRepository.deleteAll();
    }
}
```

### 2. Batch Processing for Large Tables

For very large tables, implement batch deletion to avoid memory issues:

```java
private void deleteUserRoundScoresBatch() {
    log.info("Deleting user round scores in batches");

    Pageable pageable = PageRequest.of(0, 10000);
    Page<UserRoundScore> page;

    do {
        page = userRoundScoreRepository.findAll(pageable);
        if (!page.isEmpty()) {
            userRoundScoreRepository.deleteAll(page.getContent());
            log.info("Deleted {} user round scores", page.getNumberOfElements());
        }
    } while (page.hasNext());
}
```

## Safety Measures

### 1. Backup Strategy
- Create database backup before cleanup
- Test restore procedures
- Consider point-in-time recovery options

### 2. Validation
- Implement dry-run mode to preview what will be deleted
- Validate foreign key constraints
- Test in development environment first

### 3. Rollback Plan
- Maintain transaction boundaries
- Document rollback procedures
- Have backup restoration plan ready

## Performance Considerations

### 1. Batch Size Optimization
- Use appropriate batch sizes (5000-10000 records)
- Monitor performance during deletion
- Adjust batch size based on table size and performance

### 2. Resource Management
- Schedule cleanup during low-traffic periods
- Monitor CPU and memory usage
- Consider using native SQL for very large tables

## Testing Strategy

### 1. Development Environment
- Test with production-like data volumes
- Validate deletion logic and order
- Test rollback procedures

### 2. Staging Environment
- Full end-to-end testing
- Performance validation
- Monitor system behavior during cleanup

### 3. Production Deployment
- Create full backup before execution
- Monitor system performance during cleanup
- Have rollback plan ready

## Success Metrics

### 1. Storage Reduction
- Measure database size before/after cleanup
- Track storage cost savings
- Monitor backup size reduction

### 2. Performance Improvement
- Query response time improvements
- Backup/restore time reduction
- Overall system performance enhancement


