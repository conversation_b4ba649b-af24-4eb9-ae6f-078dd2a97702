# Old Season Data Cleanup Documentation

## Overview

This document outlines the implementation of the `/admin/delete-old-season-data` endpoint, which is designed to safely and efficiently delete all data from the specified tables in the HBL Handball Manager API.

## Tables to Clean Up

### Tables to be completely cleaned

The following tables will have all their data deleted:

1. **scheduler_job** - No dependencies
2. **league_invitation** - Depends on league
3. **user_notification** - Depends on user profile
4. **user_round_score** - Depends on user profile, league, and round
5. **lineup** - Depends on team and round
6. **transfer_market_bid** - Depends on transfer_market
7. **transfer_market** - Depends on user profile, league, and player
8. **team** - Depends on user profile, league, and player

## Deletion Order

Based on the foreign key constraints, the correct deletion order is:

1. **transfer_market_bid** (depends on transfer_market)
2. **transfer_market** (depends on user_profile, league, player)
3. **lineup** (depends on team, round)
4. **user_round_score** (depends on user_profile, league, round)
5. **team** (depends on user_profile, league, player)
6. **league_invitation** (depends on league)
7. **user_notification** (depends on user_profile)
8. **scheduler_job** (no dependencies)

## Implementation Approach

### 1. Service Layer

Create a dedicated service class `OldSeasonDataCleanupService` with the following components:

```java
@Service
@Slf4j
public class OldSeasonDataCleanupService {

    @Autowired
    private TransferMarketBidRepository transferMarketBidRepository;

    @Autowired
    private TransferMarketRepository transferMarketRepository;

    @Autowired
    private LineupRepository lineupRepository;

    @Autowired
    private UserRoundScoreRepository userRoundScoreRepository;

    @Autowired
    private TeamRepository teamRepository;

    @Autowired
    private LeagueInvitationRepository leagueInvitationRepository;

    @Autowired
    private UserNotificationRepository userNotificationRepository;

    @Autowired
    private SchedulerJobRepository schedulerJobRepository;

    @Autowired
    private BatchDeletionUtil batchDeletionUtil;

    @Transactional
    public DataCleanupResponse cleanupData(DataCleanupRequest request) {
        log.info("Starting data cleanup with request: {}", request);

        Map<String, Long> deletedRecords = new HashMap<>();
        long startTime = System.currentTimeMillis();

        if (request.isDryRun()) {
            log.info("DRY RUN MODE - No actual deletions will be performed");
            // In dry run, just count records
            deletedRecords = countRecordsToDelete(request.getTables());
        } else {
            // Delete in dependency order
            if (request.getTables().contains("transfer_market_bid")) {
                deletedRecords.put("transfer_market_bid", deleteTransferMarketBids());
            }
            if (request.getTables().contains("transfer_market")) {
                deletedRecords.put("transfer_market", deleteTransferMarkets());
            }
            if (request.getTables().contains("lineup")) {
                deletedRecords.put("lineup", deleteLineups());
            }
            if (request.getTables().contains("user_round_score")) {
                deletedRecords.put("user_round_score", deleteUserRoundScores());
            }
            if (request.getTables().contains("team")) {
                deletedRecords.put("team", deleteTeams());
            }
            if (request.getTables().contains("league_invitation")) {
                deletedRecords.put("league_invitation", deleteLeagueInvitations());
            }
            if (request.getTables().contains("user_notification")) {
                deletedRecords.put("user_notification", deleteUserNotifications());
            }
            if (request.getTables().contains("scheduler_job")) {
                deletedRecords.put("scheduler_job", deleteSchedulerJobs());
            }
        }

        long executionTime = System.currentTimeMillis() - startTime;
        long totalDeleted = deletedRecords.values().stream().mapToLong(Long::longValue).sum();

        log.info("Data cleanup completed. Total deleted: {}, Execution time: {}ms",
                totalDeleted, executionTime);

        return new DataCleanupResponse(
            true,
            "Data cleanup completed successfully",
            deletedRecords,
            totalDeleted,
            formatExecutionTime(executionTime)
        );
    }

    private Map<String, Long> countRecordsToDelete(List<String> tables) {
        Map<String, Long> counts = new HashMap<>();

        if (tables.contains("transfer_market_bid")) {
            counts.put("transfer_market_bid", transferMarketBidRepository.count());
        }
        if (tables.contains("transfer_market")) {
            counts.put("transfer_market", transferMarketRepository.count());
        }
        if (tables.contains("lineup")) {
            counts.put("lineup", lineupRepository.count());
        }
        if (tables.contains("user_round_score")) {
            counts.put("user_round_score", userRoundScoreRepository.count());
        }
        if (tables.contains("team")) {
            counts.put("team", teamRepository.count());
        }
        if (tables.contains("league_invitation")) {
            counts.put("league_invitation", leagueInvitationRepository.count());
        }
        if (tables.contains("user_notification")) {
            counts.put("user_notification", userNotificationRepository.count());
        }
        if (tables.contains("scheduler_job")) {
            counts.put("scheduler_job", schedulerJobRepository.count());
        }

        return counts;
    }

    private String formatExecutionTime(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60);
        } else {
            return String.format("%02d:%02d", minutes, seconds % 60);
        }
    }

    private long deleteTransferMarketBids() {
        // Large table - use optimized batch deletion
        log.info("Deleting all transfer market bids using batch deletion");
        return batchDeletionUtil.deleteInBatches("transfer_market_bid", 10000);
    }

    private long deleteTransferMarkets() {
        // Medium table - use batch deletion
        log.info("Deleting all transfer markets using batch deletion");
        return batchDeletionUtil.deleteInBatches("transfer_market", 10000);
    }

    private long deleteLineups() {
        // Large table - use batch deletion
        log.info("Deleting all lineups using batch deletion");
        return batchDeletionUtil.deleteInBatches("lineup", 10000);
    }

    private long deleteUserRoundScores() {
        // Very large table - use batch deletion
        log.info("Deleting all user round scores using batch deletion");
        return batchDeletionUtil.deleteInBatches("user_round_score", 10000);
    }

    private long deleteTeams() {
        // Medium table - use batch deletion
        log.info("Deleting all teams using batch deletion");
        return batchDeletionUtil.deleteInBatches("team", 10000);
    }

    private long deleteLeagueInvitations() {
        // Small table - can use simple deleteAll
        log.info("Deleting all league invitations");
        long count = leagueInvitationRepository.count();
        leagueInvitationRepository.deleteAll();
        return count;
    }

    private long deleteUserNotifications() {
        // Medium table - use batch deletion
        log.info("Deleting all user notifications using batch deletion");
        return batchDeletionUtil.deleteInBatches("user_notification", 10000);
    }

    private long deleteSchedulerJobs() {
        // Small table - can use simple deleteAll
        log.info("Deleting all scheduler jobs");
        long count = schedulerJobRepository.count();
        schedulerJobRepository.deleteAll();
        return count;
    }
}
```

### 2. Optimized Batch Processing for Large Tables

For very large tables, use native SQL for better performance instead of JPA `deleteAll()`:

#### Option A: Native SQL Batch Deletion (Recommended)

```java
@Repository
public interface UserRoundScoreRepository extends JpaRepository<UserRoundScore, String> {

    @Modifying
    @Query(value = "DELETE FROM user_round_score LIMIT :chunkSize", nativeQuery = true)
    int deleteUserRoundScoresBatch(@Param("chunkSize") int chunkSize);

    @Query(value = "SELECT COUNT(*) FROM user_round_score", nativeQuery = true)
    long countAll();
}

// Service implementation
private void deleteUserRoundScoresOptimized() {
    log.info("Deleting user round scores using native SQL batches");

    int chunkSize = 10000;
    int rowsAffected;
    long totalDeleted = 0;

    do {
        rowsAffected = userRoundScoreRepository.deleteUserRoundScoresBatch(chunkSize);
        totalDeleted += rowsAffected;
        log.info("Deleted {} user round scores, total: {}", rowsAffected, totalDeleted);

        // Optional: Add small delay to reduce DB load
        if (rowsAffected > 0) {
            try {
                Thread.sleep(100); // 100ms pause between batches
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    } while (rowsAffected > 0);

    log.info("Completed deletion of user round scores. Total deleted: {}", totalDeleted);
}
```

#### Option B: ID-Based Batch Deletion (Alternative)

```java
@Repository
public interface UserRoundScoreRepository extends JpaRepository<UserRoundScore, String> {

    @Query(value = "SELECT id FROM user_round_score LIMIT :limit", nativeQuery = true)
    List<String> findIdsBatch(@Param("limit") int limit);

    @Modifying
    @Query("DELETE FROM UserRoundScore u WHERE u.id IN :ids")
    void deleteByIdInBatch(@Param("ids") List<String> ids);
}

// Service implementation
private void deleteUserRoundScoresByIds() {
    log.info("Deleting user round scores by ID batches");

    int chunkSize = 10000;
    long totalDeleted = 0;
    List<String> ids;

    do {
        ids = userRoundScoreRepository.findIdsBatch(chunkSize);
        if (!ids.isEmpty()) {
            userRoundScoreRepository.deleteByIdInBatch(ids);
            totalDeleted += ids.size();
            log.info("Deleted {} user round scores, total: {}", ids.size(), totalDeleted);
        }
    } while (!ids.isEmpty());

    log.info("Completed deletion of user round scores. Total deleted: {}", totalDeleted);
}
```

#### Option C: Generic Native SQL Utility

```java
@Component
public class BatchDeletionUtil {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Transactional
    public long deleteInBatches(String tableName, int chunkSize) {
        log.info("Deleting from {} in batches of {}", tableName, chunkSize);

        int rowsAffected;
        long totalDeleted = 0;

        do {
            rowsAffected = jdbcTemplate.update("DELETE FROM " + tableName + " LIMIT ?", chunkSize);
            totalDeleted += rowsAffected;
            log.info("Deleted {} rows from {}, total: {}", rowsAffected, tableName, totalDeleted);

            // Small pause to reduce DB load
            if (rowsAffected > 0) {
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        } while (rowsAffected > 0);

        log.info("Completed deletion from {}. Total rows deleted: {}", tableName, totalDeleted);
        return totalDeleted;
    }
}
```

## Safety Measures

### 1. Backup Strategy
- Create database backup before cleanup
- Test restore procedures
- Consider point-in-time recovery options

### 2. Validation
- Implement dry-run mode to preview what will be deleted
- Validate foreign key constraints
- Test in development environment first

### 3. Rollback Plan
- Maintain transaction boundaries
- Document rollback procedures
- Have backup restoration plan ready

## Performance Optimizations

### 1. Native SQL vs JPA Considerations

**Why Native SQL is Better for Large Deletions:**
- JPA `deleteAll()` loads entities into memory first (inefficient)
- Native SQL operates directly on the database level
- Avoids Hibernate overhead and object instantiation
- Better performance for bulk operations

**Performance Comparison:**
```
JPA deleteAll():     ~1000 records/second
Native SQL LIMIT:    ~10000+ records/second
```

### 2. Batch Size Optimization
- **Small tables** (< 100K records): Use simple `deleteAll()`
- **Medium tables** (100K - 1M records): Use 5000-10000 batch size
- **Large tables** (> 1M records): Use 10000-50000 batch size
- Monitor database performance and adjust accordingly

### 3. Memory and Resource Management
- Native SQL avoids loading entities into JVM memory
- Add small delays between batches to reduce DB load
- Schedule cleanup during low-traffic periods
- Monitor CPU, memory, and database connection usage

### 4. Database-Level Optimizations
- Ensure proper indexes exist (though they may slow deletions)
- Consider `TRUNCATE` for tables without foreign key constraints
- Run `VACUUM` and `ANALYZE` after large deletions (PostgreSQL)
- Monitor database locks and connection pools

### 5. Parallel Processing (Advanced)

For independent tables, consider parallel deletion:

```java
@Async
@Transactional
public CompletableFuture<Long> deleteSchedulerJobsAsync() {
    return CompletableFuture.completedFuture(
        batchDeletionUtil.deleteInBatches("scheduler_job", 10000)
    );
}

@Async
@Transactional
public CompletableFuture<Long> deleteUserNotificationsAsync() {
    return CompletableFuture.completedFuture(
        batchDeletionUtil.deleteInBatches("user_notification", 10000)
    );
}

// In main service
public void cleanupIndependentTablesInParallel() {
    CompletableFuture<Long> jobDeletion = deleteSchedulerJobsAsync();
    CompletableFuture<Long> notifDeletion = deleteUserNotificationsAsync();

    CompletableFuture.allOf(jobDeletion, notifDeletion).join();
    log.info("Parallel deletion completed");
}
```

⚠️ **Caution**: Only parallelize tables with no foreign key dependencies

## Testing Strategy

### 1. Development Environment
- Test with production-like data volumes
- Validate deletion logic and order
- Test rollback procedures

### 2. Staging Environment
- Full end-to-end testing
- Performance validation
- Monitor system behavior during cleanup

### 3. Production Deployment
- Create full backup before execution
- Monitor system performance during cleanup
- Have rollback plan ready

## API Endpoint Implementation

### Endpoint: `/admin/delete-old-season-data`

#### Security
- Requires admin role: `ROLE_ADMIN`
- Uses existing authentication mechanisms

#### Request Parameters

```json
{
  "dryRun": true,                  // Preview mode, no actual deletions
  "chunkSize": 10000,              // Batch size for processing large tables
  "tables": [                      // Tables to process (optional, defaults to all)
    "scheduler_job",
    "league_invitation",
    "user_notification",
    "user_round_score",
    "lineup",
    "transfer_market_bid",
    "transfer_market",
    "team"
  ]
}
```

#### Response

```json
{
  "success": true,
  "message": "Data cleanup completed successfully",
  "deletedRecords": {
    "transfer_market_bid": 4250000,
    "transfer_market": 85000,
    "lineup": 28000,
    "user_round_score": 35000,
    "team": 42000,
    "league_invitation": 450,
    "user_notification": 12500,
    "scheduler_job": 120
  },
  "totalDeleted": 4441110,
  "executionTime": "00:25:30"
}
```

### Controller Implementation

```java
@RestController
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
@Slf4j
public class DataCleanupController {

    @Autowired
    private OldSeasonDataCleanupService cleanupService;

    @PostMapping("/delete-old-season-data")
    public ResponseEntity<DataCleanupResponse> deleteOldSeasonData(
            @RequestBody DataCleanupRequest request) {

        log.info("Starting data cleanup with request: {}", request);

        try {
            DataCleanupResponse response = cleanupService.cleanupData(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error during data cleanup", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(DataCleanupResponse.error(e.getMessage()));
        }
    }
}
```

### Request/Response DTOs

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataCleanupRequest {
    private boolean dryRun = true;
    private int chunkSize = 10000;
    private List<String> tables = Arrays.asList(
        "scheduler_job", "league_invitation", "user_notification",
        "user_round_score", "lineup", "transfer_market_bid",
        "transfer_market", "team"
    );
}

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataCleanupResponse {
    private boolean success;
    private String message;
    private Map<String, Long> deletedRecords;
    private long totalDeleted;
    private String executionTime;

    public static DataCleanupResponse error(String message) {
        DataCleanupResponse response = new DataCleanupResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
}
```

## Success Metrics

### 1. Storage Reduction
- Measure database size before/after cleanup
- Track storage cost savings
- Monitor backup size reduction

### 2. Performance Improvement
- Query response time improvements
- Backup/restore time reduction
- Overall system performance enhancement


