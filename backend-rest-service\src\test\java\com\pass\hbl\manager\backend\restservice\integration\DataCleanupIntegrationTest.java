package com.pass.hbl.manager.backend.restservice.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupRequest;
import com.pass.hbl.manager.backend.persistence.dto.admin.DataCleanupResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the data cleanup functionality.
 * These tests verify the complete workflow from controller to database.
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
@Transactional
class DataCleanupIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDataCleanupEndpoint_DryRun_Success() throws Exception {
        // Arrange
        DataCleanupRequest request = new DataCleanupRequest();
        request.setDryRun(true);
        request.setChunkSize(1000);
        request.setDelayMs(0); // No delay for tests
        request.setTables(Arrays.asList("scheduler_job", "user_notification"));

        // Act
        MvcResult result = mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.dryRun").value(true))
                .andReturn();

        // Assert
        String responseContent = result.getResponse().getContentAsString();
        DataCleanupResponse response = objectMapper.readValue(responseContent, DataCleanupResponse.class);
        
        assertTrue(response.isSuccess());
        assertTrue(response.isDryRun());
        assertNotNull(response.getDeletedRecords());
        assertNotNull(response.getExecutionTime());
        assertTrue(response.getMessage().contains("preview completed successfully"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDataCleanupEndpoint_InvalidChunkSize() throws Exception {
        // Arrange
        DataCleanupRequest request = new DataCleanupRequest();
        request.setDryRun(true);
        request.setChunkSize(500); // Below minimum
        request.setTables(Arrays.asList("scheduler_job"));

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("Invalid request")));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDataCleanupEndpoint_UnsupportedTable() throws Exception {
        // Arrange
        DataCleanupRequest request = new DataCleanupRequest();
        request.setDryRun(true);
        request.setChunkSize(1000);
        request.setTables(Arrays.asList("unsupported_table"));

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("Unsupported tables")));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDataCleanupEndpoint_EmptyTableList() throws Exception {
        // Arrange
        DataCleanupRequest request = new DataCleanupRequest();
        request.setDryRun(true);
        request.setChunkSize(1000);
        request.setTables(Arrays.asList()); // Empty list

        // Act
        MvcResult result = mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andReturn();

        // Assert
        String responseContent = result.getResponse().getContentAsString();
        DataCleanupResponse response = objectMapper.readValue(responseContent, DataCleanupResponse.class);
        
        assertEquals(0L, response.getTotalDeleted());
        assertEquals(0, response.getTablesProcessed());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDataCleanupInfoEndpoint_Success() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/delete-old-season-data/info"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.supportedTables").isArray())
                .andExpect(jsonPath("$.supportedTables.length()").value(8))
                .andExpect(jsonPath("$.supportedTables[0]").value("transfer_market_bid"))
                .andExpect(jsonPath("$.supportedTables[1]").value("transfer_market"))
                .andExpect(jsonPath("$.supportedTables[2]").value("lineup"))
                .andExpect(jsonPath("$.supportedTables[3]").value("user_round_score"))
                .andExpect(jsonPath("$.supportedTables[4]").value("team"))
                .andExpect(jsonPath("$.supportedTables[5]").value("league_invitation"))
                .andExpect(jsonPath("$.supportedTables[6]").value("user_notification"))
                .andExpect(jsonPath("$.supportedTables[7]").value("scheduler_job"))
                .andExpect(jsonPath("$.defaultTables").isArray())
                .andExpect(jsonPath("$.defaultTables.length()").value(8))
                .andExpect(jsonPath("$.defaultChunkSize").value(10000))
                .andExpect(jsonPath("$.minChunkSize").value(1000))
                .andExpect(jsonPath("$.maxChunkSize").value(50000))
                .andExpect(jsonPath("$.defaultDelayMs").value(50))
                .andExpect(jsonPath("$.maxDelayMs").value(5000))
                .andExpect(jsonPath("$.description").exists());
    }

    @Test
    void testDataCleanupEndpoint_NoAuthentication() throws Exception {
        // Arrange
        DataCleanupRequest request = new DataCleanupRequest();
        request.setDryRun(true);

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void testDataCleanupEndpoint_InsufficientRole() throws Exception {
        // Arrange
        DataCleanupRequest request = new DataCleanupRequest();
        request.setDryRun(true);

        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDataCleanupEndpoint_AllSupportedTables() throws Exception {
        // Arrange
        DataCleanupRequest request = new DataCleanupRequest();
        request.setDryRun(true);
        request.setChunkSize(1000);
        request.setDelayMs(0);
        request.setTables(DataCleanupRequest.getDefaultTables());

        // Act
        MvcResult result = mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andReturn();

        // Assert
        String responseContent = result.getResponse().getContentAsString();
        DataCleanupResponse response = objectMapper.readValue(responseContent, DataCleanupResponse.class);
        
        assertTrue(response.isSuccess());
        assertTrue(response.isDryRun());
        assertEquals(8, response.getTablesProcessed());
        assertNotNull(response.getDeletedRecords());
        
        // Verify all tables are included in the response
        for (String tableName : DataCleanupRequest.getDefaultTables()) {
            assertTrue(response.getDeletedRecords().containsKey(tableName),
                "Response should contain count for table: " + tableName);
        }
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDataCleanupEndpoint_CustomParameters() throws Exception {
        // Arrange
        DataCleanupRequest request = new DataCleanupRequest();
        request.setDryRun(true);
        request.setChunkSize(5000);
        request.setDelayMs(10);
        request.setTables(Arrays.asList("scheduler_job", "league_invitation"));

        // Act
        MvcResult result = mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andReturn();

        // Assert
        String responseContent = result.getResponse().getContentAsString();
        DataCleanupResponse response = objectMapper.readValue(responseContent, DataCleanupResponse.class);
        
        assertEquals(2, response.getTablesProcessed());
        assertTrue(response.getDeletedRecords().containsKey("scheduler_job"));
        assertTrue(response.getDeletedRecords().containsKey("league_invitation"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testDataCleanupEndpoint_InvalidJson() throws Exception {
        // Act & Assert
        mockMvc.perform(post("/admin/delete-old-season-data")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("{ invalid json }"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testDataCleanupInfoEndpoint_NoAuthentication() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/delete-old-season-data/info"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER")
    void testDataCleanupInfoEndpoint_InsufficientRole() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/admin/delete-old-season-data/info"))
                .andExpect(status().isForbidden());
    }
}
