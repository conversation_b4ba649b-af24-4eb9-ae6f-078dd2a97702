package com.pass.hbl.manager.backend.persistence.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Response DTO for data cleanup operations.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response object for data cleanup operations")
public class DataCleanupResponse {

    @JsonProperty("success")
    @Schema(description = "Whether the operation was successful", example = "true")
    private boolean success;

    @JsonProperty("message")
    @Schema(description = "Operation result message", example = "Data cleanup completed successfully")
    private String message;

    @JsonProperty("dryRun")
    @Schema(description = "Whether this was a dry run (preview mode)", example = "false")
    private boolean dryRun;

    @JsonProperty("deletedRecords")
    @Schema(description = "Number of records deleted per table")
    private Map<String, Long> deletedRecords = new HashMap<>();

    @JsonProperty("totalDeleted")
    @Schema(description = "Total number of records deleted across all tables", example = "4441110")
    private long totalDeleted;

    @JsonProperty("executionTime")
    @Schema(description = "Total execution time in HH:mm:ss format", example = "00:25:30")
    private String executionTime;

    @JsonProperty("startTime")
    @Schema(description = "When the operation started")
    private LocalDateTime startTime;

    @JsonProperty("endTime")
    @Schema(description = "When the operation completed")
    private LocalDateTime endTime;

    @JsonProperty("tablesProcessed")
    @Schema(description = "Number of tables processed", example = "8")
    private int tablesProcessed;

    @JsonProperty("errors")
    @Schema(description = "Any errors that occurred during processing")
    private Map<String, String> errors = new HashMap<>();

    /**
     * Creates a successful response.
     * 
     * @param message Success message
     * @param deletedRecords Map of table names to deleted record counts
     * @param executionTime Formatted execution time
     * @param dryRun Whether this was a dry run
     * @return DataCleanupResponse instance
     */
    public static DataCleanupResponse success(String message, Map<String, Long> deletedRecords, 
                                            String executionTime, boolean dryRun) {
        DataCleanupResponse response = new DataCleanupResponse();
        response.setSuccess(true);
        response.setMessage(message);
        response.setDryRun(dryRun);
        response.setDeletedRecords(deletedRecords);
        response.setTotalDeleted(deletedRecords.values().stream().mapToLong(Long::longValue).sum());
        response.setExecutionTime(executionTime);
        response.setTablesProcessed(deletedRecords.size());
        response.setStartTime(LocalDateTime.now().minusSeconds(parseExecutionTimeToSeconds(executionTime)));
        response.setEndTime(LocalDateTime.now());
        return response;
    }

    /**
     * Creates an error response.
     * 
     * @param message Error message
     * @return DataCleanupResponse instance
     */
    public static DataCleanupResponse error(String message) {
        DataCleanupResponse response = new DataCleanupResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setDryRun(false);
        response.setTotalDeleted(0);
        response.setExecutionTime("00:00:00");
        response.setTablesProcessed(0);
        response.setStartTime(LocalDateTime.now());
        response.setEndTime(LocalDateTime.now());
        return response;
    }

    /**
     * Creates an error response with specific table errors.
     * 
     * @param message General error message
     * @param errors Map of table names to error messages
     * @return DataCleanupResponse instance
     */
    public static DataCleanupResponse errorWithDetails(String message, Map<String, String> errors) {
        DataCleanupResponse response = error(message);
        response.setErrors(errors);
        return response;
    }

    /**
     * Adds an error for a specific table.
     * 
     * @param tableName The table name
     * @param errorMessage The error message
     */
    public void addError(String tableName, String errorMessage) {
        if (errors == null) {
            errors = new HashMap<>();
        }
        errors.put(tableName, errorMessage);
    }

    /**
     * Adds deleted record count for a table.
     * 
     * @param tableName The table name
     * @param count The number of records deleted
     */
    public void addDeletedRecords(String tableName, long count) {
        if (deletedRecords == null) {
            deletedRecords = new HashMap<>();
        }
        deletedRecords.put(tableName, count);
        updateTotalDeleted();
    }

    /**
     * Updates the total deleted count based on individual table counts.
     */
    public void updateTotalDeleted() {
        if (deletedRecords != null) {
            totalDeleted = deletedRecords.values().stream().mapToLong(Long::longValue).sum();
        }
    }

    /**
     * Updates the tables processed count.
     */
    public void updateTablesProcessed() {
        if (deletedRecords != null) {
            tablesProcessed = deletedRecords.size();
        }
    }

    /**
     * Checks if there were any errors during processing.
     * 
     * @return true if there were errors, false otherwise
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }

    /**
     * Gets a summary of the operation results.
     * 
     * @return Summary string
     */
    public String getSummary() {
        if (!success) {
            return "Operation failed: " + message;
        }
        
        String mode = dryRun ? "DRY RUN" : "ACTUAL DELETION";
        return String.format("%s completed: %d records from %d tables in %s", 
                mode, totalDeleted, tablesProcessed, executionTime);
    }

    /**
     * Parses execution time string to seconds.
     * 
     * @param executionTime Time in HH:mm:ss format
     * @return Total seconds
     */
    private static long parseExecutionTimeToSeconds(String executionTime) {
        try {
            String[] parts = executionTime.split(":");
            if (parts.length == 2) {
                // mm:ss format
                return Long.parseLong(parts[0]) * 60 + Long.parseLong(parts[1]);
            } else if (parts.length == 3) {
                // HH:mm:ss format
                return Long.parseLong(parts[0]) * 3600 + Long.parseLong(parts[1]) * 60 + Long.parseLong(parts[2]);
            }
        } catch (NumberFormatException e) {
            // Ignore parsing errors
        }
        return 0;
    }
}
